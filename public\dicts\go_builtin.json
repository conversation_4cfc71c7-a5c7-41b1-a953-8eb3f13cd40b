[{"name": "bool", "trans": ["bool 是布尔值的集合，true 和 false。"]}, {"name": "true", "trans": ["true 和 false 是两个无类型布尔值。"]}, {"name": "false", "trans": ["true 和 false 是两个无类型布尔值。"]}, {"name": "uint8", "trans": ["uint8 是所有无符号8位整数的集合。范围：0 到 255。"]}, {"name": "uint16", "trans": ["uint16 是所有无符号16位整数的集合。范围：0 到 65535。"]}, {"name": "uint32", "trans": ["uint32 是所有无符号32位整数的集合。范围：0 到 4294967295。"]}, {"name": "uint64", "trans": ["uint64 是所有无符号64位整数的集合。范围：0 到 18446744073709551615。"]}, {"name": "int8", "trans": ["int8 是所有有符号8位整数的集合。范围：-128 到 127。"]}, {"name": "int16", "trans": ["int16 是所有有符号16位整数的集合。范围：-32768 到 32767。"]}, {"name": "int32", "trans": ["int32 是所有有符号32位整数的集合。范围：-2147483648 到 2147483647。"]}, {"name": "int64", "trans": ["int64 是所有有符号64位整数的集合。范围：-9223372036854775808 到 9223372036854775807。"]}, {"name": "float32", "trans": ["float32 是所有IEEE-754标准的32位浮点数的集合。"]}, {"name": "float64", "trans": ["float64 是所有IEEE-754标准的64位浮点数的集合。"]}, {"name": "complex64", "trans": ["complex64 是所有由float32实部和虚部构成的复数的集合。"]}, {"name": "complex128", "trans": ["complex128 是所有由float64实部和虚部构成的复数的集合。"]}, {"name": "string", "trans": ["string 是所有由8位字节组成的字符串的集合，通常但不一定表示UTF-8编码的文本。字符串可以为空，但不可以为nil。string类型的值是不可变的。"]}, {"name": "int", "trans": ["int 是至少为32位大小的有符号整数类型。它是一个独立的类型，不是 int32 的别名。"]}, {"name": "uint", "trans": ["uint 是至少为32位大小的无符号整数类型。它是一个独立的类型，不是 uint32 的别名。"]}, {"name": "uintptr", "trans": ["uintptr 是一个足够大以容纳任何指针的位模式的整数类型。"]}, {"name": "byte", "trans": ["byte 是 uint8 的别名，在所有方面都等同于 uint8。它通常用于区分字节值和8位无符号整数值。"]}, {"name": "rune", "trans": ["rune 是 int32 的别名，在所有方面都等同于 int32。它通常用于区分字符值和整数值。"]}, {"name": "any", "trans": ["any 是 interface{} 的别名，在所有方面都等同于 interface{}。"]}, {"name": "comparable", "trans": ["comparable 是由所有可比较类型（布尔值、数字、字符串、指针、通道、由可比较类型组成的数组、其字段全都是可比较类型的结构体）实现的接口。comparable 接口只能用作类型参数约束，不能用作变量类型。"]}, {"name": "iota", "trans": ["iota 是一个预声明的标识符，表示当前常量声明中的无类型整数序数编号（通常在括号中的 const 声明中）。它从零开始索引。"]}, {"name": "nil", "trans": ["nil 是一个预声明的标识符，表示指针、通道、函数、接口、映射或切片类型的零值。"]}, {"name": "Type", "trans": ["Type 仅用于文档目的。它是任何 Go 类型的代替，但对于任何给定的函数调用，表示相同的类型。"]}, {"name": "Type1", "trans": ["Type1 仅用于文档目的。它是任何 Go 类型的代替，但对于任何给定的函数调用，表示相同的类型。"]}, {"name": "IntegerType", "trans": ["IntegerType 仅用于文档目的。它是任何整数类型的代替：int、uint、int8 等。"]}, {"name": "FloatType", "trans": ["FloatType 仅用于文档目的。它是任何浮点类型的代替：float32 或 float64。"]}, {"name": "ComplexType", "trans": ["ComplexType 仅用于文档目的。它是任何复数类型的代替：complex64 或 complex128。"]}, {"name": "append", "trans": ["append 内建函数将元素添加到切片的末尾。如果切片具有足够的容量，则会重新切片以容纳新元素。如果没有足够的容量，将分配一个新的底层数组。append 返回更新后的切片。因此，需要存储 append 的结果，通常在保存切片本身的变量中：   tslice = append(slice, elem1, elem2)  tslice = append(slice, anotherSlice...)  作为特例，允许将字符串追加到字节切片中，如下所示：   tslice = append([]byte( hello  ),  world ...) "]}, {"name": "copy", "trans": ["copy 内建函数将元素从源切片复制到目标切片。 (作为特例，它还会将字节从字符串复制到字节切片。) 源和目标可能重叠。copy 返回复制的元素数，这将是 len(src) 和 len(dst) 中的较小者。"]}, {"name": "delete", "trans": ["delete 内建函数从映射中删除具有指定键（m[key]）的元素。如果 m 为 nil 或不存在这样的元素，则 delete 无效。"]}, {"name": "len", "trans": ["len 内建函数根据其类型返回 v 的长度：   t数组：v 中的元素数。   数组指针：*v 中的元素数（即使 v 为 nil）。           切片或映射：v 中的元素数；如果 v 为 nil，则 len(v) 为零。   字符串：v 中的字节数。           通道：通道缓冲区中排队（未读）的元素数；如果 v 为 nil，则 len(v) 为零。          对于某些参数，例如字符串字面值或简单的数组表达式，结果可以是常量。有关详细信息，请参阅 Go 语言规范中的“长度和容量”部分。"]}, {"name": "cap", "trans": ["cap 内建函数根据其类型返回 v 的容量：   t数组：v 中的元素数（与 len(v) 相同）。               数组指针：*v 中的元素数（与 len(v) 相同）。               切片：重新切片时切片可以达到的最大长度；如果 v 为 nil，则 cap(v) 为零。               通道：通道缓冲区容量，以元素为单位；如果 v 为 nil，则 cap(v) 为零。              对于某些参数，例如简单的数组表达式，结果可以是常量。有关详细信息，请参阅 Go 语言规范中的“长度和容量”部分。"]}, {"name": "make", "trans": ["make 内建函数分配并初始化类型为切片、映射或通道（仅限）的对象。与 new 不同，第一个参数是类型，而不是值。与 new 不同，make 的返回类型与其参数的类型相同，而不是其指针。结果的规范取决于类型：   t切片：size 指定长度。切片的容量等于其长度。可以提供第二个整数参数以指定不同的容量；它必须不小于长度。例如，make([]int, 0, 10) 分配一个大小为 10 的底层数组，并返回长度为 0、容量为 10 的切片，由此底层数组支持。                   映射：分配一个空映射，足够大以容纳指定数量的元素。可以省略大小，此情况下将分配一个较小的起始大小。                   通道：通道的缓冲区以指定的缓冲区容量进行初始化。如果为零，或省略大小，则通道是无缓冲的。"]}, {"name": "new", "trans": ["new 内建函数分配内存。第一个参数是类型，不是值，返回的值是指向新分配的零值的指针。"]}, {"name": "complex", "trans": ["complex 内建函数从两个浮点值构造一个复数值。实部和虚部必须具有相同的大小，即 float32 或 float64（或可分配给它们）。返回值将是相应的复数类型（float32 为 complex64，float64 为 complex128）。"]}, {"name": "real", "trans": ["real 内建函数返回复数数 c 的实部。返回值将是与 c 的类型相对应的浮点类型。"]}, {"name": "imag", "trans": ["imag 内建函数返回复数数 c 的虚部。返回值将是与 c 的类型相对应的浮点类型。"]}, {"name": "close", "trans": ["close 内建函数关闭通道，必须是双向的或只发送的。它只能由发送者执行，永远不会由接收者执行，并且在接收到最后一个发送的值后，会关闭通道。从关闭通道 c 接收到的任何接收将不会阻塞，会返回通道元素的零值。形式如   tx, ok := <-c  也会为已关闭且为空的通道设置 ok 为 false。"]}, {"name": "panic", "trans": ["panic 内建函数停止当前 Goroutine 的正常执行。当函数 F 调用 panic 时，F 的正常执行立即停止。由 F 推迟执行的任何函数都会按照通常的方式运行，然后 F 返回给其调用者。对于调用者 G，对 F 的调用则像是对 panic 的调用，终止 G 的执行并运行任何推迟的函数。这将一直继续，直到执行 Goroutine 中的所有函数都停止，以相反的顺序进行。在那时，程序将以非零退出码终止。此终止序列称为 panic，可以通过内建函数 recover 来控制。"]}, {"name": "recover", "trans": ["recover 内建函数允许程序管理处于 panic 状态的 Goroutine 的行为。在延迟函数（但不是由它调用的任何函数）中执行 recover 调用会通过恢复正常执行来停止 panic 序列，并检索传递给 panic 调用的错误值。如果在延迟函数之外调用 recover，则不会停止 panic 序列。在这种情况下，或者 Goroutine 不在 panic 状态，或者传递给 panic 的参数为 nil，recover 返回 nil。因此，从 recover 返回的返回值报告 Goroutine 是否处于 panic 状态。"]}, {"name": "print", "trans": ["print 内建函数以特定于实现的方式格式化其参数，并将结果写入标准错误。print 用于引导和调试；不保证保留在语言中。"]}, {"name": "println", "trans": ["println 内建函数以特定于实现的方式格式化其参数，并将结果写入标准错误。参数之间始终添加空格，并附加换行符。println 用于引导和调试；不保证保留在语言中。"]}, {"name": "error", "trans": ["error 内建接口类型是表示错误条件的传统接口，nil 值表示没有错误。"]}]