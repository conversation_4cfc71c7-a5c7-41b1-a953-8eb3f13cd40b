[{"name": "Accuracy", "trans": ["准确率"]}, {"name": "Activation Function", "trans": ["激活函数"]}, {"name": "Active Learning", "trans": ["主动学习"]}, {"name": "Area Under ROC Curve", "trans": ["AUC（ROC曲线下方面积，度量分类模型好坏的标准）"]}, {"name": "Artificial Intelligence", "trans": ["人工智能"]}, {"name": "Artificial Neural Network", "trans": ["人工神经网络"]}, {"name": "Attention", "trans": ["注意力"]}, {"name": "Back Propagation", "trans": ["反向传播"]}, {"name": "Bagging", "trans": ["袋装"]}, {"name": "Baseline", "trans": ["基准"]}, {"name": "Bayesian Inference", "trans": ["贝叶斯推断"]}, {"name": "Bayesian Network", "trans": ["贝叶斯网/贝叶斯网络"]}, {"name": "Bernoulli Distribution", "trans": ["伯努利分布"]}, {"name": "Bias", "trans": ["偏差/偏置"]}, {"name": "Biased", "trans": ["有偏"]}, {"name": "Boosting", "trans": ["Boosting（一种模型训练加速方式）"]}, {"name": "Bottom-Up", "trans": ["自下而上"]}, {"name": "Classification", "trans": ["分类"]}, {"name": "Classification And Regression Tree", "trans": ["分类与回归树"]}, {"name": "Cluster", "trans": ["簇"]}, {"name": "Computer Vision", "trans": ["计算机视觉"]}, {"name": "Confusion Matrix", "trans": ["混淆矩阵"]}, {"name": "Conjugate Gradient", "trans": ["共轭梯度"]}, {"name": "Correlation", "trans": ["相关系数"]}, {"name": "Cost Function", "trans": ["代价函数"]}, {"name": "Covariance", "trans": ["协方差"]}, {"name": "Covariance Matrix", "trans": ["协方差矩阵"]}, {"name": "Data Augmentation", "trans": ["数据增强"]}, {"name": "Data Mining", "trans": ["数据挖掘"]}, {"name": "Data Set", "trans": ["数据集"]}, {"name": "Decision Tree", "trans": ["决策树"]}, {"name": "Deep Learning", "trans": ["深度学习"]}, {"name": "Deep Neural Network", "trans": ["深度神经网络"]}, {"name": "Deep Reinforcement Learning", "trans": ["深度强化学习"]}, {"name": "Dimension Reduction", "trans": ["降维"]}, {"name": "Dimensionality Reduction Algorithm", "trans": ["降维算法"]}, {"name": "Encoder-Decoder", "trans": ["编码器-解码器（模型）"]}, {"name": "Error Function", "trans": ["误差函数"]}, {"name": "Estimator", "trans": ["估计/估计量"]}, {"name": "Feature Engineering", "trans": ["特征工程"]}, {"name": "Feature Extraction", "trans": ["特征抽取"]}, {"name": "Feature Selection", "trans": ["特征选择"]}, {"name": "Feedforward Neural Network", "trans": ["前馈神经网络"]}, {"name": "Gaussian Distribution", "trans": ["高斯分布"]}, {"name": "Gaussian Kernel Function", "trans": ["高斯核函数"]}, {"name": "Gaussian Mixtures", "trans": ["高斯混合（模型）"]}, {"name": "Gaussian Process", "trans": ["高斯过程"]}, {"name": "Gaussian Process Regression", "trans": ["高斯过程回归"]}, {"name": "Generative Modeling", "trans": ["生成式建模"]}, {"name": "Genetic Algorithm", "trans": ["遗传算法"]}, {"name": "<PERSON><PERSON><PERSON> Descent", "trans": ["梯度下降"]}, {"name": "Grid Search", "trans": ["网格搜索"]}, {"name": "Ground Truth", "trans": ["真实值"]}, {"name": "Hyperplane", "trans": ["超平面"]}, {"name": "Inductive Bias", "trans": ["归纳偏好"]}, {"name": "Information Gain", "trans": ["信息增益"]}, {"name": "Information Gain <PERSON>", "trans": ["信息增益比"]}, {"name": "Iteration", "trans": ["迭代"]}, {"name": "K-Fold Cross Validation", "trans": ["k 折交叉验证"]}, {"name": "K-Means Clustering", "trans": ["k-均值聚类"]}, {"name": "K-Nearest Neighbor Method", "trans": ["k-近邻"]}, {"name": "Kernel Method", "trans": ["核方法"]}, {"name": "Kernel Trick", "trans": ["核技巧"]}, {"name": "Label", "trans": ["标签/标记"]}, {"name": "Lazy Learning", "trans": ["懒惰学习"]}, {"name": "Linear Combination", "trans": ["线性组合"]}, {"name": "Linear Discriminant Analysis", "trans": ["线性判别分析"]}, {"name": "Linear Model", "trans": ["线性模型"]}, {"name": "Linear Regression", "trans": ["线性回归"]}, {"name": "Logistic Function", "trans": ["对数几率函数"]}, {"name": "Logistic Regression", "trans": ["对数几率回归"]}, {"name": "Long Short Term Memory", "trans": ["长短期记忆"]}, {"name": "Loss Function", "trans": ["损失函数"]}, {"name": "Machine Learning", "trans": ["机器学习"]}, {"name": "<PERSON><PERSON>", "trans": ["间隔"]}, {"name": "Meta-Learning", "trans": ["元学习"]}, {"name": "Metric", "trans": ["指标"]}, {"name": "Model Predictive Control", "trans": ["模型预测控制"]}, {"name": "Model Selection", "trans": ["模型选择"]}, {"name": "Multi-Layer Perceptron", "trans": ["多层感知机"]}, {"name": "Multiple Linear Regression", "trans": ["多元线性回归"]}, {"name": "Natural Language Processing", "trans": ["自然语言处理"]}, {"name": "Neural Model", "trans": ["神经模型"]}, {"name": "Neural Network", "trans": ["神经网络"]}, {"name": "Noise", "trans": ["噪声"]}, {"name": "Non-Parametric", "trans": ["非参数"]}, {"name": "Normalization", "trans": ["规范化"]}, {"name": "Occam's Razor", "trans": ["奥卡姆剃刀"]}, {"name": "One-Shot Learning", "trans": ["单试学习"]}, {"name": "Orthogonal", "trans": ["正交"]}, {"name": "Outlier", "trans": ["异常点"]}, {"name": "Output Layer", "trans": ["输出层"]}, {"name": "Overfitting", "trans": ["过拟合"]}, {"name": "Parameter Tuning", "trans": ["调参"]}, {"name": "Parse Tree", "trans": ["解析树"]}, {"name": "Particle Swarm Optimization", "trans": ["粒子群优化算法"]}, {"name": "Pattern Recognition", "trans": ["模式识别"]}, {"name": "Perceptron", "trans": ["感知机"]}, {"name": "Precision", "trans": ["查准率/准确率"]}, {"name": "Principal Component Analysis", "trans": ["主成分分析"]}, {"name": "Prior Knowledge", "trans": ["先验知识"]}, {"name": "Probability Distribution", "trans": ["概率分布"]}, {"name": "Quantum Mechanics", "trans": ["量子力学"]}, {"name": "Radial Basis Function", "trans": ["径向基函数"]}, {"name": "Random Forest", "trans": ["随机森林"]}, {"name": "Random Sampling", "trans": ["随机采样"]}, {"name": "Recall", "trans": ["查全率/召回率"]}, {"name": "Receiver Operating Characteristic", "trans": ["受试者工作特征"]}, {"name": "Rectified Linear Unit", "trans": ["修正线性单元/整流线性单元"]}, {"name": "Recurrent Neural Network", "trans": ["循环神经网络"]}, {"name": "Regression", "trans": ["回归"]}, {"name": "Reinforcement Learning", "trans": ["强化学习"]}, {"name": "Representation Learning", "trans": ["表示学习"]}, {"name": "Rob<PERSON><PERSON>", "trans": ["稳健性"]}, {"name": "Sequence-To-Sequence", "trans": ["序列到序列"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["<PERSON><PERSON><PERSON><PERSON>（一种激活函数）"]}, {"name": "Simulated Annealing", "trans": ["模拟退火"]}, {"name": "Singular", "trans": ["奇异的"]}, {"name": "Softmax Function", "trans": ["Softmax函数/软最大化函数"]}, {"name": "Speech Recognition", "trans": ["语音识别"]}, {"name": "Statistical Learning", "trans": ["统计学习"]}, {"name": "Supervised Learning", "trans": ["监督学习"]}, {"name": "Support Vector", "trans": ["支持向量"]}, {"name": "Support Vector Machine", "trans": ["支持向量机"]}, {"name": "Support Vector Regression", "trans": ["支持向量回归"]}, {"name": "Test Set", "trans": ["测试集"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["阈值"]}, {"name": "Top-Down", "trans": ["自顶向下"]}, {"name": "Training Sample", "trans": ["训练样本"]}, {"name": "Training Set", "trans": ["训练集"]}, {"name": "Trajectory", "trans": ["轨迹"]}, {"name": "Transfer Learning", "trans": ["迁移学习"]}, {"name": "True Negative", "trans": ["真负例"]}, {"name": "True Positive", "trans": ["真正例"]}, {"name": "True Positive Rate", "trans": ["真正例率"]}, {"name": "Underfitting", "trans": ["欠拟合"]}, {"name": "Unsupervised Learning", "trans": ["无监督学习"]}, {"name": "Validation Set", "trans": ["验证集"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["方差"]}, {"name": "Variational Autoencoder", "trans": ["变分自编码器"]}, {"name": "Weight", "trans": ["权重"]}, {"name": "Word Embedding", "trans": ["词嵌入"]}, {"name": "2D Qsar Models", "trans": ["二维定量构效关系模型"]}, {"name": "3D Cartesian", "trans": ["三维笛卡尔（坐标）"]}, {"name": "3D Conformation", "trans": ["三维构象"]}, {"name": "3D Grids", "trans": ["三维（坐标）网格"]}, {"name": "3D Qsar Models", "trans": ["三维定量构效关系模型"]}, {"name": "Aberration-Corrected", "trans": ["像差矫正"]}, {"name": "Active Machine Learning", "trans": ["主动机器学习"]}, {"name": "Adaptive Fuzzy Neural Network", "trans": ["自适应模糊神经网络"]}, {"name": "Adaptive Sampling", "trans": ["自适应采样"]}, {"name": "Admet Evaluation", "trans": ["毒性评估"]}, {"name": "Alexnet", "trans": ["AlexNet"]}, {"name": "Alphago", "trans": ["阿尔法狗"]}, {"name": "Adaptive Neuro Fuzzy Inference System", "trans": ["自适应神经模糊推理系统"]}, {"name": "Approximate Probabilistic Models", "trans": ["近似概率模型"]}, {"name": "Artificial Neurons", "trans": ["人工神经元"]}, {"name": "Artificial Synapses", "trans": ["人工突触"]}, {"name": "Attention-Based", "trans": ["基于注意力（机制）的"]}, {"name": "Automating Synthetic Planning", "trans": ["自动化综合规划"]}, {"name": "Automation", "trans": ["自动化"]}, {"name": "Autonomous Decision-Making", "trans": ["自主决策"]}, {"name": "B-Clustering Algorithms", "trans": ["B树聚类算法"]}, {"name": "Balanced Accuracy", "trans": ["平衡精度"]}, {"name": "Bandgap Energy", "trans": ["带隙能量"]}, {"name": "Baseline Test", "trans": ["基准测试"]}, {"name": "Basin Hopping", "trans": ["盆地跳跃（算法）"]}, {"name": "Bayesian Approach", "trans": ["贝叶斯方法"]}, {"name": "Bayesian Induction", "trans": ["贝叶斯归纳"]}, {"name": "Bayesian Mcmc Methods", "trans": ["贝叶斯马尔可夫链蒙特卡洛方法"]}, {"name": "Bayesian Methods", "trans": ["贝叶斯方法"]}, {"name": "Bayesian Molecular", "trans": ["贝叶斯分子（设计方法）"]}, {"name": "Bayesian Prior", "trans": ["贝叶斯先验"]}, {"name": "Bayesian Program Learning", "trans": ["贝叶斯程序学习"]}, {"name": "Bayesian Regularized Neural Network", "trans": ["贝叶斯正则化神经网络"]}, {"name": "Beam-Scanning", "trans": ["波束扫描"]}, {"name": "Best Separates", "trans": ["最优分离"]}, {"name": "Biased Dataset", "trans": ["有偏数据集"]}, {"name": "Bit Collisions", "trans": ["字节碰撞/冲突"]}, {"name": "Black Box", "trans": ["黑盒子"]}, {"name": "Black-Box Attack", "trans": ["黑盒攻击"]}, {"name": "Bonding Environments", "trans": ["成键环境"]}, {"name": "Bonferroni Correction", "trans": ["邦弗朗尼校正"]}, {"name": "Bootstrap Aggregation", "trans": ["引导聚合"]}, {"name": "B<PERSON>den<PERSON>–Goldfarb–Shan<PERSON>", "trans": ["BFGS（算法）"]}, {"name": "Buchwald−Hartwig Cross-Coupling", "trans": ["Buchwald–Hartwig 偶联（反应）"]}, {"name": "C4.5 Algorithm", "trans": ["C4.5 算法"]}, {"name": "Calculation Uncertainties", "trans": ["计算不确定性"]}, {"name": "Canonical Ml Methods", "trans": ["经典机器学习方法"]}, {"name": "Cartesian Distance Vector", "trans": ["笛卡尔距离向量"]}, {"name": "CASP", "trans": ["国际蛋白质结构预测竞赛"]}, {"name": "Categorical Data", "trans": ["分类数据"]}, {"name": "Categorization Algorithms", "trans": ["分类算法"]}, {"name": "ChemDataExtractor", "trans": ["化学数据提取器"]}, {"name": "Chi-Squared", "trans": ["卡方（分布）"]}, {"name": "Classification Model", "trans": ["分类模型"]}, {"name": "Cluster Resolution Feature Selection", "trans": ["聚类分辨率特征选择"]}, {"name": "Cluster-Based Splitting", "trans": ["基于聚类的分离方法"]}, {"name": "Clustering Methods", "trans": ["聚类方法"]}, {"name": "Code Pipeline", "trans": ["代码流水线"]}, {"name": "Coefficient of Determination", "trans": ["决定系数"]}, {"name": "Combined Gradient", "trans": ["组合梯度（算法）"]}, {"name": "Complex Data", "trans": ["复合数据"]}, {"name": "Computational Cost", "trans": ["计算成本"]}, {"name": "Computational Optimisation", "trans": ["计算优化"]}, {"name": "Computational Science", "trans": ["计算科学"]}, {"name": "Computational Toxicology", "trans": ["计算毒理学"]}, {"name": "Computer Science", "trans": ["计算机科学"]}, {"name": "Computer Simulations", "trans": ["计算机模拟"]}, {"name": "Computer-Aided", "trans": ["计算机辅助"]}, {"name": "Constraint", "trans": ["约束"]}, {"name": "Core-Loss Spectrum", "trans": ["（电子能量损失谱中的）高能区域"]}, {"name": "Coulomb Matrix", "trans": ["库仑矩阵"]}, {"name": "Coupled-Cluster Predictions", "trans": ["耦合簇预测"]}, {"name": "Cross-Validated Coefficient of Determination", "trans": ["交叉验证的决定系数"]}, {"name": "Cross-Validation", "trans": ["交叉验证"]}, {"name": "Crowd-Sourcing", "trans": ["众包"]}, {"name": "Cut-Points", "trans": ["切点"]}, {"name": "Cutoff Radial Function", "trans": ["截断径向函数"]}, {"name": "Data Availability", "trans": ["数据可用性"]}, {"name": "Data Cleaning", "trans": ["数据清洗"]}, {"name": "Data Collection", "trans": ["数据采集"]}, {"name": "Data Considerations", "trans": ["数据注意事项"]}, {"name": "Data Curation", "trans": ["数据监管"]}, {"name": "Data Disparity", "trans": ["数据差异"]}, {"name": "Data Dredging", "trans": ["数据挖掘"]}, {"name": "Data Imputation", "trans": ["数据填补"]}, {"name": "Data Labels", "trans": ["数据标签"]}, {"name": "Data Leakage", "trans": ["数据泄露"]}, {"name": "Data Pre-Processing", "trans": ["数据预处理"]}, {"name": "Data Processing", "trans": ["数据处理"]}, {"name": "Data Quality", "trans": ["数据质量"]}, {"name": "Data Reduction", "trans": ["数据缩减"]}, {"name": "Data Representation", "trans": ["数据表示"]}, {"name": "Data Selection", "trans": ["数据选择"]}, {"name": "Data Sources", "trans": ["数据源"]}, {"name": "Data Splitting", "trans": ["数据拆分"]}, {"name": "Data Transformation", "trans": ["数据转换"]}, {"name": "Data-Driven", "trans": ["数据驱动"]}, {"name": "Data-Driven Decision-Making", "trans": ["数据驱动的决策"]}, {"name": "Data-Driven Methods", "trans": ["数据驱动的方法"]}, {"name": "Data-Driven Spectral Analysis", "trans": ["数据驱动的光谱分析"]}, {"name": "Data-Mining", "trans": ["数据挖掘"]}, {"name": "Database", "trans": ["数据库"]}, {"name": "DE Algorithm", "trans": ["差分进化算法"]}, {"name": "Deeplift", "trans": ["DeepLift模型"]}, {"name": "Dendrogram", "trans": ["树状图"]}, {"name": "Density Functional Theory", "trans": ["密度泛函理论"]}, {"name": "Density-Based Spatial Clustering Of Applications With Noise", "trans": ["DBSCAN密度聚类"]}, {"name": "Descriptor", "trans": ["描述符"]}, {"name": "DFT Calculations", "trans": ["DFT计算"]}, {"name": "Dice Similarity", "trans": ["戴斯相似度"]}, {"name": "Differential Evolution", "trans": ["差分进化"]}, {"name": "Dimensionality Reduction", "trans": ["降维"]}, {"name": "Direct Neural Network Modeling", "trans": ["正向神经网络建模"]}, {"name": "<PERSON><PERSON><PERSON>", "trans": ["离散方式"]}, {"name": "Discrete Quanta", "trans": ["离散量子"]}, {"name": "Discretization", "trans": ["离散化"]}, {"name": "Distillation", "trans": ["蒸馏"]}, {"name": "Dynamic Datasets", "trans": ["动态数据集"]}, {"name": "Dynamic Filter Networks", "trans": ["动态过滤网络"]}, {"name": "Dynamic Sampling", "trans": ["动态采样"]}, {"name": "Dynamics Simulations", "trans": ["动力学模拟"]}, {"name": "Eigenfunction", "trans": ["特征函数"]}, {"name": "Electronegativity", "trans": ["电负性"]}, {"name": "<PERSON><PERSON>", "trans": ["埃尔曼"]}, {"name": "Empirical Models", "trans": ["经验模型"]}, {"name": "Energy Derivatives", "trans": ["能源衍生品"]}, {"name": "Energy Potentials", "trans": ["能量潜力"]}, {"name": "Ensemble Methods", "trans": ["集成方法"]}, {"name": "Entity Normalisation", "trans": ["实体规范化"]}, {"name": "Ethical Considerations", "trans": ["道德考虑"]}, {"name": "Euclidean Distances", "trans": ["欧几里得距离"]}, {"name": "Evolutionary Algorithms", "trans": ["进化算法"]}, {"name": "Evolutionary Method", "trans": ["进化方法"]}, {"name": "Exchange–Correlation", "trans": ["交换关联（的能量/泛函）"]}, {"name": "Excited-State Potentials", "trans": ["激发态能量"]}, {"name": "Expected Reduction In Distortion", "trans": ["符合预期的失真减少"]}, {"name": "Experimental Validation Data", "trans": ["实验验证数据"]}, {"name": "Expert Systems", "trans": ["专家系统"]}, {"name": "Extended-Connectivity Circular Fingerprint", "trans": ["扩展连接环形指纹"]}, {"name": "Extraction Techniques", "trans": ["提取技术"]}, {"name": "Faber<PERSON><PERSON><PERSON><PERSON><PERSON>", "trans": ["Faber<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "Facial Recognition", "trans": ["面部识别"]}, {"name": "FAIR Data Principles", "trans": ["FAIR数据原则"]}, {"name": "False Negatives", "trans": ["假阴性"]}, {"name": "False Positives", "trans": ["假阳性"]}, {"name": "Fchl Representation", "trans": ["Fchl 表示"]}, {"name": "Feature Binarization", "trans": ["特征二值化"]}, {"name": "Feature Transform", "trans": ["特征变换"]}, {"name": "Feature Vectors", "trans": ["特征向量"]}, {"name": "Features", "trans": ["特征"]}, {"name": "Feed Back", "trans": ["反馈"]}, {"name": "Feed-Forward Neural Networks", "trans": ["前馈神经网络"]}, {"name": "Feedback Structure", "trans": ["反馈结构"]}, {"name": "Final Evaluation", "trans": ["最终评估"]}, {"name": "Findable", "trans": ["Accessible"]}, {"name": "First-Principles", "trans": ["第一性原理"]}, {"name": "Flow Rate", "trans": ["流速"]}, {"name": "Forward Cross-Validation", "trans": ["前向交叉验证"]}, {"name": "Forward Prediction", "trans": ["前向预测"]}, {"name": "Forward Reaction Prediction", "trans": ["前向反应预测"]}, {"name": "Fuzzy Logic", "trans": ["模糊逻辑"]}, {"name": "Fuzzy Neural Networks", "trans": ["模糊神经网络"]}, {"name": "Ga-Based Approaches", "trans": ["基于遗传算法的方法"]}, {"name": "Garbage In", "trans": ["Garbage Out"]}, {"name": "Gas-Phase Networks", "trans": ["气相网络"]}, {"name": "Gaussian <PERSON>", "trans": ["高斯核"]}, {"name": "Gaussian-Type Structure Descriptors", "trans": ["高斯型结构描述符"]}, {"name": "General Intelligence", "trans": ["通用智能"]}, {"name": "Generalized Gradient Approximation", "trans": ["广义梯度近似"]}, {"name": "Generative Adversarial Networks", "trans": ["生成对抗网络"]}, {"name": "Gradient Boosting Decision Tree", "trans": ["梯度提升决策树"]}, {"name": "Gradient-Based", "trans": ["基于梯度的"]}, {"name": "Grain-Surface Networks", "trans": ["粒面网络"]}, {"name": "Graph Convolutional", "trans": ["图卷积"]}, {"name": "Graph Models", "trans": ["图模型"]}, {"name": "Graph Neural Networks", "trans": ["图神经网络"]}, {"name": "Graph-Based", "trans": ["基于图形"]}, {"name": "Graph-Based Models", "trans": ["基于图的模型"]}, {"name": "Graph-Based Neural Networks", "trans": ["基于图的神经网络"]}, {"name": "Graph-Based Representation", "trans": ["基于图的表示"]}, {"name": "Graph-Convolutional Neural Network", "trans": ["图卷积神经网络"]}, {"name": "Graphics Processing Units", "trans": ["图形处理器"]}, {"name": "Gravimetric Polymerization Degree", "trans": ["比重聚合度"]}, {"name": "Hamiltonian Matrix", "trans": ["哈密顿矩阵"]}, {"name": "Hamiltonian Operator", "trans": ["哈密顿算符"]}, {"name": "Heterogeneous Data", "trans": ["异构数据"]}, {"name": "Hidden Layers", "trans": ["隐藏层"]}, {"name": "High Data Throughput", "trans": ["高数据吞吐量"]}, {"name": "High Throughput", "trans": ["高通量"]}, {"name": "High Throughput Screening", "trans": ["高通量筛选"]}, {"name": "High Variance Models", "trans": ["高方差模型"]}, {"name": "High-Dimensional Data", "trans": ["高维数据"]}, {"name": "High-Dimensional NN", "trans": ["高维神经网络"]}, {"name": "High-Dimensional Objects", "trans": ["高维对象"]}, {"name": "High-Throughput", "trans": ["高通量"]}, {"name": "Higher-Dimensional Space", "trans": ["高维空间"]}, {"name": "Higher-Dimensional Spectral Space", "trans": ["高维光谱空间"]}, {"name": "Homogenization", "trans": ["同质化"]}, {"name": "Homomorphic Encryption", "trans": ["同态加密"]}, {"name": "Human Face Recognition", "trans": ["人脸识别"]}, {"name": "Human-Encoded", "trans": ["人工编码的"]}, {"name": "Hybrid Model", "trans": ["混合模型"]}, {"name": "Hybrid Technique", "trans": ["混合技术"]}, {"name": "Hybrid-Neural Model", "trans": ["混合神经模型"]}, {"name": "Hyperparameter Opimization", "trans": ["超参数优化"]}, {"name": "Hyperparameters", "trans": ["超参数"]}, {"name": "Hyperplanes Separate", "trans": ["超平面分离"]}, {"name": "Id3 Algorithm", "trans": ["Id3 算法"]}, {"name": "Image And Speech Recognition", "trans": ["图像和语音识别"]}, {"name": "Image Classification", "trans": ["图像分类"]}, {"name": "Image Classifier", "trans": ["图像分类器"]}, {"name": "Image Recognition", "trans": ["图像识别"]}, {"name": "Informative Priors", "trans": ["信息先验"]}, {"name": "Input-Output Pairs", "trans": ["输入输出对"]}, {"name": "Instance-Based", "trans": ["基于实例的"]}, {"name": "Intelligent Machine", "trans": ["智能机器"]}, {"name": "Intermediate Neurons", "trans": ["中间神经元"]}, {"name": "Internet Of Things", "trans": ["物联网"]}, {"name": "Interpolation Coordinate", "trans": ["插值坐标"]}, {"name": "Interpretability", "trans": ["可解释性"]}, {"name": "Inverse Neural Modeling", "trans": ["逆神经建模"]}, {"name": "Inverse Neural Network Modeling", "trans": ["逆神经网络建模"]}, {"name": "Iterative Learning", "trans": ["迭代学习"]}, {"name": "Joint Distribution", "trans": ["联合分布"]}, {"name": "Jordan-<PERSON>an <PERSON>", "trans": ["Jordan-<PERSON><PERSON> 神经网络"]}, {"name": "K Clusters", "trans": ["K聚类"]}, {"name": "K Nearest Points", "trans": ["K 最近点"]}, {"name": "K-1 Folds", "trans": ["K-1 折"]}, {"name": "<PERSON><PERSON><PERSON> (O-K Edge)", "trans": ["K-边缘（O-K 边缘）"]}, {"name": "K-Means", "trans": ["K-均值"]}, {"name": "Kendall’S Tau", "trans": ["肯德尔等级相关系数"]}, {"name": "Kernel Ridge Regression", "trans": ["核岭回归"]}, {"name": "Kernels", "trans": ["内核"]}, {"name": "Kinetic Curve", "trans": ["动力学曲线"]}, {"name": "KNN Model", "trans": ["K 近邻模型"]}, {"name": "Knowledge Extraction", "trans": ["知识提取"]}, {"name": "Knowledge Gradient", "trans": ["知识梯度"]}, {"name": "L1 And L2 Regularization", "trans": ["L1与L2正则化"]}, {"name": "Laboratory Level", "trans": ["实验室级别"]}, {"name": "Language Processing", "trans": ["语言处理"]}, {"name": "Laplacian Prior", "trans": ["拉普拉斯先验"]}, {"name": "Large-Scale Data Storage", "trans": ["大规模数据存储"]}, {"name": "Lasers", "trans": ["激光器"]}, {"name": "Lasso Regression", "trans": ["拉索回归"]}, {"name": "LBP", "trans": ["局部二值模式"]}, {"name": "Least Absolute Shrinkage And Selection Operator", "trans": ["Lasso回归"]}, {"name": "Least Square Support Vector Machine", "trans": ["最小二乘支持向量机"]}, {"name": "Ligand-Field", "trans": ["配位场"]}, {"name": "Linear", "trans": ["线性的"]}, {"name": "Linear Dimension Reduction Methods", "trans": ["线性降维方法"]}, {"name": "Linear Vibronic Coupling Model", "trans": ["线性振子耦合模型"]}, {"name": "Local Recurrent", "trans": ["本地卷积"]}, {"name": "Logic And Heuristics Applied To Synthetic Analysis", "trans": ["LHASA 程序"]}, {"name": "Long-Range Prediction", "trans": ["长期预测"]}, {"name": "Long-Range Prediction Models", "trans": ["长期预测模型"]}, {"name": "Long-Term Planning", "trans": ["长期规划"]}, {"name": "<PERSON>-<PERSON><PERSON>", "trans": ["长期回报"]}, {"name": "Machine-Readable Data", "trans": ["机器可读的数据"]}, {"name": "<PERSON>", "trans": ["平均绝对误差"]}, {"name": "Mahalanobis Distances", "trans": ["马氏距离"]}, {"name": "Matrices", "trans": ["矩阵"]}, {"name": "Matthews Correlation Coefficient", "trans": ["马修斯相关系数"]}, {"name": "Maximum Likelihood Methods", "trans": ["最大似然法"]}, {"name": "Maximum Likelihood Procedures", "trans": ["最大似然估计法"]}, {"name": "MCTS Method", "trans": ["蒙特卡洛树搜索方法"]}, {"name": "Mean-Squared <PERSON><PERSON><PERSON>", "trans": ["均方误差"]}, {"name": "Mechanical Sympathy", "trans": ["机械同感，软硬件协同编程"]}, {"name": "Merging", "trans": ["合并"]}, {"name": "Message Passing Neural Networks", "trans": ["消息传递神经网络"]}, {"name": "Microarray Data", "trans": ["微阵列数据"]}, {"name": "Mini Batch", "trans": ["小批次"]}, {"name": "Mining", "trans": ["挖掘"]}, {"name": "Mining Out", "trans": ["挖掘"]}, {"name": "Missing Values", "trans": ["缺失值"]}, {"name": "ML Algorithm", "trans": ["机器学习算法"]}, {"name": "ML Modelling", "trans": ["机器学习建模"]}, {"name": "ML Potentials", "trans": ["机器学习势能"]}, {"name": "ML-Driven", "trans": ["机器学习驱动的"]}, {"name": "ML-Driven Optimization", "trans": ["机器学习驱动的最优化"]}, {"name": "MLP Neural Model", "trans": ["多层感知机神经模型"]}, {"name": "Model Construction", "trans": ["模型构建"]}, {"name": "Model Evaluation", "trans": ["模型评估"]}, {"name": "Model Performance", "trans": ["模型性能"]}, {"name": "Model Statistics", "trans": ["模型统计"]}, {"name": "Model Training", "trans": ["模型训练"]}, {"name": "Model Validation", "trans": ["模型验证"]}, {"name": "Model-Based Iterative Reconstruction", "trans": ["基于模型的迭代重建"]}, {"name": "Model-Construction", "trans": ["模型构建"]}, {"name": "Modelling <PERSON><PERSON><PERSON>", "trans": ["建模场景"]}, {"name": "Molecular Graph Theory", "trans": ["分子图论"]}, {"name": "Molecular Modelling", "trans": ["分子建模"]}, {"name": "Monte Carlo Tree Search", "trans": ["蒙特卡洛树搜索"]}, {"name": "<PERSON><PERSON>S Law", "trans": ["摩尔定律"]}, {"name": "ms-QSBER-EL Model", "trans": ["基于人工神经网络组合的结构生物学效应定量关系多尺度模型"]}, {"name": "Multi-Agent Control System", "trans": ["多智能体控制系统"]}, {"name": "Multi-Core Desktop Computer", "trans": ["多核台式计算机"]}, {"name": "Multi-Dimensional Big Data Analysis", "trans": ["多维度大数据分析"]}, {"name": "Multi-Layer Feed-Forward", "trans": ["多层前馈"]}, {"name": "Multi-Objective Genetic Algorithm", "trans": ["多目标遗传算法"]}, {"name": "Multi-Objective Optimization", "trans": ["多目标优化"]}, {"name": "Multi-Reaction Synthesis", "trans": ["多反应合成"]}, {"name": "Multilayer Perceptron", "trans": ["多层感知机"]}, {"name": "Multivariate Regression", "trans": ["多变量回归"]}, {"name": "N-Dimensional Space", "trans": ["N维空间"]}, {"name": "<PERSON><PERSON>", "trans": ["朴素贝叶斯"]}, {"name": "Naive Bayesian Methods", "trans": ["朴素贝叶斯方法"]}, {"name": "Named Entity Recognition，NER", "trans": ["命名实体识别"]}, {"name": "Nearest Neighbors", "trans": ["近邻"]}, {"name": "Nearest Neighbour Model", "trans": ["近邻模型"]}, {"name": "Negative Predictive Value", "trans": ["阴性预测值"]}, {"name": "Network Architecture", "trans": ["网络结构"]}, {"name": "Network Geometry", "trans": ["网络几何"]}, {"name": "Neural Turing Machines", "trans": ["神经图灵机"]}, {"name": "Neural-Network-Based Function", "trans": ["基于神经网络的函数"]}, {"name": "Neurons", "trans": ["神经元"]}, {"name": "Nuclear Magnetic Resonance", "trans": ["核磁共振"]}, {"name": "Noise Filters", "trans": ["噪声过滤器"]}, {"name": "Noise-Free", "trans": ["无噪的"]}, {"name": "Non-Linear", "trans": ["非线性"]}, {"name": "Non-Linear Correlation", "trans": ["非线性相关"]}, {"name": "Non-Linearity", "trans": ["非线性"]}, {"name": "Non-Parametric Algorithm", "trans": ["非参数化学习算法"]}, {"name": "Non-Safety-Critical Applications", "trans": ["非安全关键型应用"]}, {"name": "Non-Steady-State", "trans": ["非稳态"]}, {"name": "Non-Stochastic", "trans": ["非随机的"]}, {"name": "Non-Template", "trans": ["非模板"]}, {"name": "Non-Template Methods", "trans": ["非模板方法"]}, {"name": "Non-Zero Weight", "trans": ["非零权重"]}, {"name": "On-The-Fly Optimization", "trans": ["运行中优化"]}, {"name": "One-Hot Vector", "trans": ["独热向量"]}, {"name": "Open-Source", "trans": ["开源"]}, {"name": "Open-Source Dataset", "trans": ["开源数据集"]}, {"name": "Predicted Label", "trans": ["预测值"]}, {"name": "Prediction", "trans": ["预测"]}, {"name": "Prediction Accuracy", "trans": ["预测准确率"]}, {"name": "Predictor", "trans": ["预测器/决策函数"]}, {"name": "Protein Folding", "trans": ["蛋白折叠"]}, {"name": "Quantum Chemistry", "trans": ["量子化学"]}, {"name": "Quantum Theory", "trans": ["量子理论"]}, {"name": "Random Selection", "trans": ["随机选择"]}, {"name": "Raw Datasets", "trans": ["原始数据集"]}, {"name": "Root Mean Square Errors", "trans": ["均方根"]}, {"name": "Sc<PERSON>", "trans": ["缩放"]}, {"name": "Simulation", "trans": ["仿真"]}, {"name": "The Global Minimum", "trans": ["全局最小值"]}, {"name": "Turing Test", "trans": ["图灵测试"]}, {"name": "Version Control", "trans": ["版本控制"]}, {"name": "Workflow", "trans": ["工作流"]}, {"name": "Sequence-Function", "trans": ["序列-功能"]}]