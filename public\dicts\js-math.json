[{"name": "Math.PI", "trans": ["Math.PI 表示一个圆的周长与直径的比例，约为 3.14159："]}, {"name": "Math.SQRT1_2", "trans": ["Math.SQRT1_2 属性表示 1/2 的平方根，约为 0.707："]}, {"name": "Math.SQRT2", "trans": ["Math.SQRT2 属性表示 2 的平方根，约为 1.414："]}, {"name": "Math.abs()", "trans": ["Math.abs(x) 函数返回指定数字 “x“ 的绝对值。如下："]}, {"name": "Math.acos()", "trans": ["Math.acos() 返回一个数的反余弦值（单位为弧度），即："]}, {"name": "<PERSON>.acosh()", "trans": ["Math.acosh()返回一个数字的反双曲余弦值，即："]}, {"name": "Math.asin()", "trans": ["Math.asin() 方法返回一个数值的反正弦（单位为弧度），即："]}, {"name": "<PERSON><PERSON>asinh()", "trans": ["Math.asinh() 函数返回给定数字的反双曲正弦值, 即："]}, {"name": "<PERSON>.atan()", "trans": ["Math.atan() 函数返回一个数值的反正切（以弧度为单位），即："]}, {"name": "Math.atan2()", "trans": ["Math.atan2() 返回其参数比值的反正切值。"]}, {"name": "<PERSON>.atanh()", "trans": ["Math.atanh() 函数返回一个数值反双曲正切值, 即："]}, {"name": "Math.cbrt()", "trans": ["Math.cbrt() 函数返回任意数字的立方根."]}, {"name": "Math.ceil()", "trans": ["Math.ceil() 函数返回大于或等于一个给定数字的最小整数。"]}, {"name": "Math.clz32()", "trans": ["Math.clz32() 函数返回一个数字在转换成 32 无符号整形数字的二进制形式后, 开头的 0 的个数, 比如 1000000 转换成 32 位无符号整形数字的二进制形式后是 00000000000011110100001001000000, 开头的 0 的个数是 12 个, 则 Math.clz32(1000000) 返回 12."]}, {"name": "Math.cos()", "trans": ["Math.cos() 函数返回一个数值的余弦值。"]}, {"name": "<PERSON>.cosh()", "trans": ["Math.cosh() 函数返回数值的双曲余弦函数, 可用 constant e 表示:"]}, {"name": "Math.exp()", "trans": ["Math.exp() 函数返回 ex，x 表示参数，e 是欧拉常数（<PERSON><PERSON><PERSON>'s constant），自然对数的底数。"]}, {"name": "Math.expm1()", "trans": ["Math.expm1() 函数返回 Ex - 1, 其中 x 是该函数的参数, E 是自然对数的底数 2.718281828459045."]}, {"name": "Math.floor()", "trans": ["Math.floor() 返回小于或等于一个给定数字的最大整数。"]}, {"name": "Math.fround()", "trans": ["Math.fround() 可以将任意的数字转换为离它最近的单精度浮点数形式的数字。"]}, {"name": "Math.hypot()", "trans": ["Math.hypot() 函数返回它的所有参数的平方和的平方根，即："]}, {"name": "<PERSON>.imul()", "trans": ["该函数返回两个参数的类C的32位整数乘法运算的运算结果."]}, {"name": "Math.log()", "trans": ["Math.log() 函数返回一个数的自然对数，即："]}, {"name": "Math.log10()", "trans": ["Math.log10() 函数返回一个数字以 10 为底的对数."]}, {"name": "Math.log1p()", "trans": ["Math.log1p() 函数返回一个数字加1后的自然对数 (底为 E), 既log(x+1)."]}, {"name": "Math.log2()", "trans": ["Math.log2() 函数返回一个数字以 2 为底的对数."]}, {"name": "Math.max()", "trans": ["Math.max() 函数返回一组数中的最大值。"]}, {"name": "Math.min()", "trans": ["Math.min() 返回零个或更多个数值的最小值。"]}, {"name": "Math.pow()", "trans": ["Math.pow() 函数返回基数（base）的指数（exponent）次幂，即 baseexponent。"]}, {"name": "Math.random()", "trans": ["Math.random() 函数返回一个浮点,  伪随机数在范围[0，1)，也就是说，从0（包括0）往上，但是不包括1（排除1），然后您可以缩放到所需的范围。实现将初始种子选择到随机数生成算法;它不能被用户选择或重置。他不能被用户选择或重置。"]}, {"name": "Math.round()", "trans": ["Math.round() 函数返回一个数字四舍五入后最接近的整数。"]}, {"name": "Math.sign()", "trans": ["Math.sign() 函数返回一个数字的符号, 指示数字是正数，负数还是零。"]}, {"name": "Math.sin()", "trans": ["Math.sin() 函数返回一个数值的正弦值。"]}, {"name": "<PERSON>.sinh()", "trans": ["Math.sinh() 函数返回一个数字(单位为角度)的双曲正弦值."]}, {"name": "Math.sqrt()", "trans": ["Math.sqrt() 函数返回一个数的平方根，即："]}, {"name": "Math.tan()", "trans": ["Math.tan() 方法返回一个数值的正切值。"]}, {"name": "<PERSON>.tanh()", "trans": ["Math.tanh() 函数将会返回一个数的双曲正切函数值，计算如下:"]}, {"name": "<PERSON>.trunc()", "trans": ["Math.trunc() 方法会将数字的小数部分去掉，只保留整数部分。"]}]