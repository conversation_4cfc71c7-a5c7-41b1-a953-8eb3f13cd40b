[{"name": "abs", "trans": ["返回一个数的绝对值"]}, {"name": "all", "trans": ["如果 iterable对象 的所有元素均为真值则返回 True"]}, {"name": "any", "trans": ["如果 iterable对象 的任一元素为真值则返回 True"]}, {"name": "ascii", "trans": ["返回一个包含对象的可打印表示形式的字符串"]}, {"name": "bin", "trans": ["将一个整数转变为二进制字符串"]}, {"name": "bool", "trans": ["布尔"]}, {"name": "breakpoint", "trans": ["此函数会在调用时将你陷入调试器中"]}, {"name": "bytearray", "trans": ["返回一个新的 bytes 数组"]}, {"name": "bytes", "trans": ["返回一个新的 bytes 对象"]}, {"name": "callable", "trans": ["检测对象是否可调用"]}, {"name": "chr", "trans": ["返回 Unicode 码位为整数 i 的字符的字符串格式"]}, {"name": "complex", "trans": ["返回值为 real + imag*1j 的复数"]}, {"name": "delattr", "trans": ["删除对象指定的属性"]}, {"name": "dict", "trans": ["创建一个新的字典"]}, {"name": "dir", "trans": ["如果没有实参，则返回当前本地作用域中的名称列表。如果有实参，它会尝试返回该对象的有效属性列表。"]}, {"name": "divmod", "trans": ["执行整数除法时返回一对商和余数"]}, {"name": "enumerate", "trans": ["返回一个枚举对象"]}, {"name": "eval", "trans": ["解析并求值一个 Python 表达式"]}, {"name": "exec", "trans": ["执行 Python 代码"]}, {"name": "filter", "trans": ["用 iterable 中函数 function 返回真的那些元素"]}, {"name": "float", "trans": ["返回从数字或字符串 x 生成的浮点数"]}, {"name": "format", "trans": ["将 value 转换为 format_spec 控制的 格式化 表示"]}, {"name": "frozenset", "trans": ["返回一个新的 不可变集合 对象"]}, {"name": "getattr", "trans": ["返回对象命名属性的值"]}, {"name": "globals", "trans": ["返回表示当前全局符号表的字典"]}, {"name": "has<PERSON>r", "trans": ["检测对象是否含有指定属性"]}, {"name": "hash", "trans": ["返回该对象的哈希值"]}, {"name": "help", "trans": ["启动内置的帮助系统"]}, {"name": "hex", "trans": ["将整数转换为以 0x 为前缀的小写十六进制字符串"]}, {"name": "id", "trans": ["返回对象的 标识值 "]}, {"name": "input", "trans": ["从输入中读取一行"]}, {"name": "int", "trans": ["返回一个基于数字或字符串 x 构造的整数对象"]}, {"name": "isinstance", "trans": ["检测对象是否为类的实例"]}, {"name": "issubclass", "trans": ["检测类是否为类的子类"]}, {"name": "iter", "trans": ["返回一个 iterable 对象"]}, {"name": "len", "trans": ["返回对象的长度（元素个数）"]}, {"name": "list", "trans": ["创建一个新的列表"]}, {"name": "locals", "trans": ["更新并返回表示当前本地符号表的字典"]}, {"name": "map", "trans": ["返回一个将 function 应用于 iterable 中每一项并输出其结果的迭代器"]}, {"name": "max", "trans": ["返回可迭代对象中最大的元素"]}, {"name": "memoryview", "trans": ["返回由给定实参创建的 内存视图 对象"]}, {"name": "min", "trans": ["返回可迭代对象中最小的元素"]}, {"name": "next", "trans": ["通过调用 iterator 的 __next__() 方法获取下一个元素"]}, {"name": "object", "trans": ["返回一个没有特征的新对象"]}, {"name": "oct", "trans": ["将一个整数转变为一个前缀为 0o 的八进制字符串"]}, {"name": "open", "trans": ["打开 file 并返回对应的 file object"]}, {"name": "ord", "trans": ["对表示单个 Unicode 字符的字符串"]}, {"name": "pow", "trans": ["返回 base 的 exp 次幂"]}, {"name": "print", "trans": ["将 objects 打印到 file 指定的文本流"]}, {"name": "property", "trans": ["返回 property 属性"]}, {"name": "range", "trans": ["创建一个整数列表"]}, {"name": "repr", "trans": ["返回包含一个对象的可打印表示形式的字符串"]}, {"name": "reversed", "trans": ["返回一个反向的 iterator"]}, {"name": "round", "trans": ["回 number 舍入到小数点后 ndigits 位精度的值"]}, {"name": "set", "trans": ["返回一个新的 set 对象"]}, {"name": "setattr", "trans": ["设置对象属性"]}, {"name": "slice", "trans": ["返回一个指定索引集的 slice 对象"]}, {"name": "sorted", "trans": ["根据 iterable 中的项返回一个新的已排序列表"]}, {"name": "str", "trans": ["返回一个 str 版本的 object"]}, {"name": "sum", "trans": ["自左向右对 iterable 的项求和并返回总计值"]}, {"name": "super", "trans": ["返回一个代理对象"]}, {"name": "tuple", "trans": ["返回一个新的 元组 对象"]}, {"name": "type", "trans": ["返回 object 的类型"]}, {"name": "vars", "trans": ["返回模块、类、实例或任何其它具有 __dict__ 属性的对象的 __dict__ 属性"]}, {"name": "zip", "trans": ["创建一个聚合了来自每个可迭代对象中的元素的迭代器"]}]