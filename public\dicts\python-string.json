[{"name": "capitalize()", "trans": ["将字符串的第一个字母变成大写,其他字母变小写。对于 8 位字节编码需要根据本地环境。"]}, {"name": "center()", "trans": ["返回一个原字符串居中,并使用空格填充至长度 width 的新字符串。默认填充字符为空格。"]}, {"name": "count()", "trans": ["用于统计字符串里某个字符出现的次数。可选参数为在字符串搜索的开始与结束位置。"]}, {"name": "decode()", "trans": ["以 encoding 指定的编码格式解码字符串。默认编码为字符串编码。"]}, {"name": "encode()", "trans": ["以 encoding 指定的编码格式编码字符串。errors参数可以指定不同的错误处理方案。"]}, {"name": "endswith()", "trans": ["用于判断字符串是否以指定后缀结尾，如果以指定后缀结尾返回True，否则返回False。可选参数'start'与'end'为检索字符串的开始与结束位置。"]}, {"name": "expandtabs()", "trans": ["把字符串中的 tab 符号('\t')转为空格，默认的空格数 tabsize 是 8。"]}, {"name": "find()", "trans": ["检测字符串中是否包含子字符串 str ，如果指定 beg（开始） 和 end（结束） 范围，则检查是否包含在指定范围内，如果包含子字符串返回开始的索引值，否则返回-1。"]}, {"name": "format()", "trans": ["调用此方法的字符串可以包含文字文本或用大括号{}分隔的替换字段。每个替换字段包含位置参数的数字索引，或关键字参数的名称。"]}, {"name": "index()", "trans": ["检测字符串中是否包含子字符串str ，如果指定 beg（开始） 和 end（结束） 范围，则检查是否包含在指定范围内，该方法与 python find()方法一样，只不过如果str不在 string中会报一个异常。"]}, {"name": "isalnum()", "trans": ["检测字符串是否由字母和数字组成"]}, {"name": "isalpha()", "trans": ["检测字符串是否只由字母组成"]}, {"name": "isdecimal()", "trans": ["检查字符串是否只包含十进制字符。这种方法只存在于unicode对象"]}, {"name": "isdigit()", "trans": ["检测字符串是否只由数字组成"]}, {"name": "islower()", "trans": ["检测字符串是否由小写字母组成。"]}, {"name": "isnumeric()", "trans": ["检测字符串是否只由数字组成。这种方法是只针对unicode对象"]}, {"name": "isspace()", "trans": ["检测字符串是否只由空格组成"]}, {"name": "istitle()", "trans": ["检测字符串中所有的单词拼写首字母是否为大写，且其他字母为小写"]}, {"name": "isupper()", "trans": ["检测字符串中所有的字母是否都为大写"]}, {"name": "join()", "trans": ["用于将序列中的元素以指定的字符连接生成一个新的字符串"]}, {"name": "ljust()", "trans": ["返回一个原字符串左对齐,并使用空格填充至指定长度的新字符串。如果指定的长度小于原字符串的长度则返回原字符串"]}, {"name": "lower()", "trans": ["转换字符串中所有大写字符为小写"]}, {"name": "lstrip()", "trans": ["用于截掉字符串左边的空格或指定字符"]}, {"name": "partition()", "trans": ["用来根据指定的分隔符将字符串进行分割"]}, {"name": "replace()", "trans": ["把字符串中的 old（旧字符串） 替换成 new(新字符串)，如果指定第三个参数max，则替换不超过 max 次"]}, {"name": "rfind()", "trans": ["返回字符串最后一次出现的位置，如果没有匹配项则返回-1"]}, {"name": "rindex()", "trans": ["返回子字符串 str 在字符串中最后出现的位置，如果没有匹配的字符串会报异常，你可以指定可选参数[beg:end]设置查找的区间"]}, {"name": "rjust()", "trans": ["返回一个原字符串右对齐,并使用空格填充至长度 width 的新字符串。如果指定的长度小于字符串的长度则返回原字符串。"]}, {"name": "rpartition()", "trans": ["从目标字符串的末尾也就是右边开始搜索分割符"]}, {"name": "rsplit()", "trans": ["通过指定分隔符对字符串进行分割并返回一个列表,默认分隔符为所有空字符"]}, {"name": "rstrip()", "trans": ["删除 string 字符串末尾的指定字符（默认为空格）."]}, {"name": "split()", "trans": ["通过指定分隔符对字符串进行切片，如果参数num 有指定值，则仅分隔 num 个子字符串。"]}, {"name": "splitlines()", "trans": ["按照行分隔，返回一个包含各行作为元素的列表，如果 num 指定则仅切片 num 个行."]}, {"name": "startswith()", "trans": ["用于检查字符串是否是以指定子字符串开头，如果是则返回 True，否则返回 False。如果参数 beg 和 end 指定值，则在指定范围内检查。"]}, {"name": "strip()", "trans": ["用于移除字符串头尾指定的字符（默认为空格）"]}, {"name": "swapcase", "trans": ["用于对字符串的大小写字母进行转换"]}, {"name": "title()", "trans": ["返回‘标题化’的字符串,就是说所有单词都是以大写开始，其余字母均为小写(见 istitle())"]}, {"name": "translate()", "trans": ["根据参数table给出的表(包含 256 个字符)转换字符串的字符, 要过滤掉的字符放到 del 参数中"]}, {"name": "upper()", "trans": ["将字符串中的小写字母转为大写字母"]}, {"name": "zfill()", "trans": ["返回指定长度的字符串，原字符串右对齐，前面填充0。"]}]