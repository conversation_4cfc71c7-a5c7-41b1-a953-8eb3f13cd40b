[{"name": "altsep", "trans": ["另一种可以替代使用的文件路径分隔符，如果所在的系统支持其他的分隔符，那么可以使用os.altsep来使用系统支持的其他类型的分隔符，如果系统不支持，那么该值为None，如在Windows中，os.altsep为‘/’。"]}, {"name": "curdir", "trans": ["返回当前目录: ('.')。"]}, {"name": "defpath", "trans": ["当使用exec函数族的时候，如果没有制定PATH环境变量，则默认会查找os.defpath中的值作为子进程PATH的值。"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "trans": ["在不同的系统上null设备的路径，在Windows下为‘nul’，在POSIX下为‘/dev/null’"]}, {"name": "extsep", "trans": ["文件名和文件扩展名之间分隔的符号，在Windows下为‘.’"]}, {"name": "linesep", "trans": ["输出当前平台使用的行终止符，win下为'\t\n',Linux下为'\n'。"]}, {"name": "pardir", "trans": ["获取当前目录的父目录字符串名：('..')"]}, {"name": "pathsep", "trans": ["PATH环境变量中的分隔符，在POSIX系统中为‘:’，在Windows中为‘;’"]}, {"name": "sep", "trans": ["不同的平台有不同的路径表示方法，为了在编写代码的时候方便处理，增加可移植性，可以使用os.sep作为路径的分隔符"]}, {"name": "argv", "trans": ["命令行参数"]}, {"name": "builtin_module_names", "trans": ["链接c模块"]}, {"name": "byteorder", "trans": ["返回本机字节顺序"]}, {"name": "check_-interval", "trans": ["信号检测频率"]}, {"name": "exec_prefix", "trans": ["根目录"]}, {"name": "executable", "trans": ["可执行文件的名称"]}, {"name": "exitfunc", "trans": ["退出函数名"]}, {"name": "modules", "trans": ["加载模块"]}, {"name": "path", "trans": ["搜索路径"]}, {"name": "platform", "trans": ["查看当前操作系统的信息,来采集系统版本位数计算机类型名称内核等一系列信息"]}, {"name": "stdin", "trans": ["标准输入，stdin提供了read()和readline()函数，如果想按一行行来读取，可以考虑使用它"]}, {"name": "stdout", "trans": ["标准输出"]}, {"name": "stderr", "trans": ["错误输出"]}, {"name": "version_info", "trans": ["获取python版本号"]}, {"name": "winver", "trans": ["版本号"]}]